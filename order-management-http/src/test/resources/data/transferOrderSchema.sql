-- Create transfer_order table
CREATE TABLE IF NOT EXISTS transfer_order (
    id UUID PRIMARY KEY,
    source_dc_code VARCHAR(255) NOT NULL,
    destination_dc_code VARCHAR(255) NOT NULL,
    creator_email VARCHAR(255) NOT NULL,
    reason_text TEXT,
    status VARCHAR(255) NOT NULL,
    year_week VARCHAR(255) NOT NULL,
    transfer_order_number VARCHAR(255) NOT NULL,
    source_dc_name VARCHAR(255),
    market_code VARCHAR(255),
    version INTEGER,
    total_price_currency_code VARCHAR(3),
    total_price_units BIGINT,
    total_price_nanos INTEGER,
    delivery_start_time TIMESTAMP,
    delivery_end_time TIMESTAMP,
    shipping JSONB,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Create transfer_order_item table
CREATE TABLE IF NOT EXISTS transfer_order_item (
    id UUID PRIMARY KEY,
    transfer_order_id UUID NOT NULL,
    sku_id UUID,
    supplier_id UUID,
    csku_code VARCHAR(255),
    csku_name VARCHAR(255),
    order_size INTEGER,
    packaging_type VARCHAR(255),
    case_packaging_size_value VARCHAR(255),
    case_packaging_size_unit VARCHAR(255),
    price_currency_code VARCHAR(3),
    price_units BIGINT,
    price_nanos INTEGER,
    total_price_currency_code VARCHAR(3),
    total_price_units BIGINT,
    total_price_nanos INTEGER,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIGN KEY (transfer_order_id) REFERENCES transfer_order(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transfer_order_year_week ON transfer_order(year_week);
CREATE INDEX IF NOT EXISTS idx_transfer_order_destination_dc ON transfer_order(destination_dc_code);
CREATE INDEX IF NOT EXISTS idx_transfer_order_source_dc ON transfer_order(source_dc_code);
CREATE INDEX IF NOT EXISTS idx_transfer_order_item_transfer_order_id ON transfer_order_item(transfer_order_id);
CREATE INDEX IF NOT EXISTS idx_transfer_order_item_sku_id ON transfer_order_item(sku_id);
