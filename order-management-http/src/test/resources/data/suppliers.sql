INSERT INTO public.supplier (id, market, code, name, status, currency, type, created_at, updated_at, city, country, state, address, number, post_code, parent_id, dc_codes) VALUES
('35661b1f-edd1-4ce2-ad33-f7c982086b2a', 'us', '115836', 'Supplier C', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '35661b1f-edd1-4ce2-ad33-f7c982086b2a', '{AA,BB,CC}'),
('2a19e55a-3b88-4c4e-81f6-978b8ad72eac', 'us', '115837', 'Supplier B', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '2a19e55a-3b88-4c4e-81f6-978b8ad72eac', '{AA,BB,CC}'),
('31f4b1ee-6cdb-4cb8-9123-ba754d0bc04f', 'us', '115838', 'Supplier A', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '31f4b1ee-6cdb-4cb8-9123-ba754d0bc04f', '{AA,BB,CC}'),
('2ac0d291-f983-4af1-a297-9585fd7c38b4', 'us', '115839', 'Supplier F', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '2ac0d291-f983-4af1-a297-9585fd7c38b4', '{AA,BB,CC}'),
('a4541145-8779-4de6-b1b8-3d07608136ac', 'us', '115840', 'Supplier G', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'a4541145-8779-4de6-b1b8-3d07608136ac', '{AA,BB,CC}'),
('46e7bd06-6eb9-4c15-ac06-8ea4b29aa969', 'us', '115841', 'Supplier E', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '46e7bd06-6eb9-4c15-ac06-8ea4b29aa969', '{AA,BB,CC}'),
('5f79dd0f-b3fa-40b9-9868-0959a5860fdd', 'us', '115842', 'Supplier Z', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '5f79dd0f-b3fa-40b9-9868-0959a5860fdd', '{AA,BB,CC}'),
('91050b27-f0bf-4754-bb92-ae8677a12df9', 'us', '115843', 'Supplier 115845', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '91050b27-f0bf-4754-bb92-ae8677a12df9', '{AA,BB,CC}'),
('2b4cf69c-fe85-48f4-9d49-ef20f751be66', 'us', '115844', 'Supplier D', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', '2b4cf69c-fe85-48f4-9d49-ef20f751be66', '{AA,BB,CC}'),
('d578e414-f301-4e0d-92f6-393f14abb145', 'us', '115845', 'Supplier O', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb145', '{AA,BB,CC}'),
('d578e414-f301-4e0d-92f6-393f14abb146', 'us', '115845', 'Supplier I', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb146', '{DD}'),
('d578e414-f301-4e0d-92f6-393f14abb156', 'us', '115845', 'Supplier Ohio', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb146', '{DD}'),
('d578e414-f301-4e0d-92f6-393f14abb147', 'dach', '115845', 'German supplier', 'ONBOARDING', 'EUR', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Berlin', 'DE', 'Berlin', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb147', '{VE}'),
('d578e414-f301-4e0d-92f6-393f14abb148', 'us', '115845', 'Supplier Excluded', 'ARCHIVED', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb145', '{XX}'),
('d578e414-f301-4e0d-92f6-393f14abb149', 'us', '115845', 'Supplier Not Excluded', 'ONBOARDING', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'London', 'CA', 'London', 'Consequuntur in lore', '193', 'IG11 7PS', 'd578e414-f301-4e0d-92f6-393f14abb145', '{XX}'),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'us', 'SUP-001', 'Test Supplier A', 'ACTIVE', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'New York', 'US', 'NY', '123 Test Street', '100', '10001', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '{DC001,DC002}'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'us', 'SUP-002', 'Test Supplier B', 'ACTIVE', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Los Angeles', 'US', 'CA', '456 Test Avenue', '200', '90001', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '{DC001,DC002}')
;


INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '35661b1f-edd1-4ce2-ad33-f7c982086b2a', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'EE', '2a19e55a-3b88-4c4e-81f6-978b8ad72eac', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '31f4b1ee-6cdb-4cb8-9123-ba754d0bc04f', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '2ac0d291-f983-4af1-a297-9585fd7c38b4', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', 'a4541145-8779-4de6-b1b8-3d07608136ac', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '46e7bd06-6eb9-4c15-ac06-8ea4b29aa969', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '5f79dd0f-b3fa-40b9-9868-0959a5860fdd', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '91050b27-f0bf-4754-bb92-ae8677a12df9', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', '2b4cf69c-fe85-48f4-9d49-ef20f751be66', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', 'd578e414-f301-4e0d-92f6-393f14abb145', 'VENDOR', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'XX', 'd578e414-f301-4e0d-92f6-393f14abb145', 'CROSSDOCK', 'us');
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES (uuid_generate_v4(), 'AA', 'd578e414-f301-4e0d-92f6-393f14abb156', 'VENDOR', 'us');
