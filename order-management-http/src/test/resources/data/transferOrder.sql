INSERT INTO transfer_order (
    id,
    source_dc_code,
    destination_dc_code,
    creator_email,
    reason_text,
    status,
    year_week,
    transfer_order_number,
    source_dc_name,
    market_code,
    version,
    total_price_currency_code,
    total_price_units,
    total_price_nanos,
    delivery_start_time,
    delivery_end_time,
    shipping,
    created_at,
    updated_at
) VALUES 
(
    '11111111-1111-1111-1111-111111111111',
    'DC001',
    'DC002',
    '<EMAIL>',
    'Stock transfer',
    'STATE_OPEN',
    '2024-W01',
    'TO-2024-001',
    'DC001 - Test Warehouse',
    'US',
    1,
    'USD',
    25000,
    0,
    '2024-01-15 10:00:00',
    '2024-01-15 18:00:00',
    '{"method": "Truck", "address": {"regionCode": "US", "postalCode": "12345", "addressLines": ["123 Main St"]}}',
    '2024-01-01 10:00:00',
    '2024-01-01 10:00:00'
),
(
    '22222222-2222-2222-2222-222222222222',
    'DC001',
    'DC002',
    '<EMAIL>',
    'Regular transfer',
    'STATE_IN_TRANSIT',
    '2024-W01',
    'TO-2024-002',
    'DC001 - Test Warehouse',
    'US',
    1,
    'USD',
    50000,
    0,
    '2024-01-16 09:00:00',
    '2024-01-16 17:00:00',
    '{"method": "Standard", "address": {"regionCode": "US", "postalCode": "67890", "addressLines": ["456 Oak Ave"]}}',
    '2024-01-02 10:00:00',
    '2024-01-02 10:00:00'
);
