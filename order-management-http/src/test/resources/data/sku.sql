INSERT INTO public.sku (uuid, market, name, code, status, brands, category)
VALUES (uuid_generate_v4(), 'us', 'Chicken, Sausage - 12 Ounce (oz)', 'PTN-10-10101-7', 'INACTIVE', '{HelloFresh}', 'PTN'),
       (uuid_generate_v4(), 'us', 'SE Freebies Mixed Jan', 'OTH-33-121101-1', 'ONBOARDING', '{HelloFresh}', 'OTH'),
       (uuid_generate_v4(), 'us', 'SE test', 'OTH-33-121101-2', 'ONBOARDING', '{HelloFresh}', 'OTH'),
       (uuid_generate_v4(), 'jp', 'aluminium coolpouch / アルミ保冷袋  - Msize', 'PCK-81-103553-3', 'ACTIVE', '{HelloFresh}', 'PCK'),
       (uuid_generate_v4(), 'gb', '<PERSON><PERSON><PERSON>uce - 75g', 'PRO-10-80049-1', 'ACTIVE', '{HelloFresh,Green Chef}', 'PRO'),
       (uuid_generate_v4(), 'us', 'Coconut Milk - South, NO Additives', 'PRO-00-105599-1', 'ACTIVE', '{HelloFresh}', 'PRO'),
       (uuid_generate_v4(), 'ca', 'X3B- Tortillas, White Flour 6-inch (6pc)', 'BAK-10-003294-7', 'ACTIVE', '{HelloFresh,Chef’s Plate}', 'BAK'),
       (uuid_generate_v4(), 'beneluxfr', 'Persil plat et coriandre (10g) - FR', 'PHF-13-003354-4', 'ARCHIVED', '{HelloFresh}', 'PHF'),
       (uuid_generate_v4(), 'dach', 'Krustenschinken 180g - 72414 - QS', 'PTN-11-90550-2', 'INACTIVE', '{HelloFresh}', 'PTN'),
       (uuid_generate_v4(), 'beneluxfr', 'Pomme de terre Grenailles (250g) - FR', 'PHF-13-101290-4', 'ONBOARDING', '{HelloFresh}', 'PHF'),
       (uuid_generate_v4(), 'ca', 'DO NOT USE - X3B- Bun, Brioche (1pc)', 'BAK-10-004062-1', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'BAK'),
       ('00000000-0000-0000-0001-000000000000', 'us', 'DO NOT USE - 1', 'BAK-10-004062-2', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'BAK'),
       ('00000000-0000-0000-0002-000000000000', 'us', 'DO NOT USE - 2', 'BAK-10-004062-3', 'ACTIVE', '{Chef’s Plate,HelloFresh}', 'BAK'),
       ('00000000-0000-0000-0003-000000000000', 'ca', 'DO NOT USE - 3', 'BAK-10-004062-4', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'BAK'),
       ('00000000-0000-0000-0004-000000000000', 'us', 'No Supplier', 'BAK-10-004062-4', 'ACTIVE', '{Chef’s Plate,HelloFresh}', 'BAK'),
       ('7a291c6d-688a-41f3-b0ec-7ff760aa7a6a', 'us', 'Chicken wings from New Jersey', 'PRO-10-005501-5', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'PRO'),
       ('dfaa3a9f-cb9d-4f80-8b4e-7ec550442c21', 'us', 'Vegan burgers from New Jersey', 'VEG-14-005501-9', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'VEG'),
       ('11111111-1111-1111-1111-111111111111', 'us', 'Test Product', 'SKU-001', 'ACTIVE', '{HelloFresh}', 'Fresh'),
       ('*************-2222-2222-************', 'us', 'Another Product', 'SKU-002', 'ACTIVE', '{HelloFresh}', 'Dairy');

INSERT INTO public.supplier_sku (uuid, parent_supplier_id, sku_id, market, status, supplier_code)
VALUES ('00000000-0000-0000-0000-000000000000', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0001-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000001', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0002-000000000000', 'us', 'ACTIVE','0002'),
       ('00000000-0000-0000-0000-000000000002', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0003-000000000000', 'us', 'ACTIVE','0003'),
       ('00000000-0000-0000-0000-000000000003', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0004-000000000000', 'us', 'ACTIVE','0003');
