package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class TransferOrderOverviewMapperTest {

    @Nested
    @DisplayName("calculateConsolidatedStatus")
    inner class CalculateConsolidatedStatusTest {

        @Test
        fun `should return DELIVERY_REJECTED when all breakdown items have Delivery Rejected status`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Delivery Rejected"),
                    createBreakdownItem(status = "Delivery Rejected"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal.ZERO,
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.DELIVERY_REJECTED, result.status)
        }

        @Test
        fun `should return RECEIVED_PARTIAL_REJECTION when any breakdown item has Delivery Rejected status`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Delivery Rejected"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("10.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION, result.status)
        }

        @Test
        fun `should return RECEIVED_PARTIAL_REJECTION when any breakdown item has Received - Partial Rejection status`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Received - Partial Rejection"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("15.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION, result.status)
        }

        @Test
        fun `should return RECEIVED_UNDER when total received quantity is less than total ordered quantity`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Received"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("15.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_UNDER, result.status)
        }

        @Test
        fun `should return RECEIVED_OVER when total received quantity is greater than total ordered quantity`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Received"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("25.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_OVER, result.status)
        }

        @Test
        fun `should return RECEIVED_ACCURATE when total received quantity equals total ordered quantity`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Received"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("20.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_ACCURATE, result.status)
        }

        @Test
        fun `should return IN_PROGRESS_HJ when total received quantity is null`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "In Transit"),
                    createBreakdownItem(status = "Pending"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = null,
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.IN_PROGRESS_HJ, result.status)
        }

        @Test
        fun `should prioritize delivery rejection over quantity comparison`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Delivery Rejected"),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("20.0"), // Would be accurate if not for rejection
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION, result.status)
        }

        @Test
        fun `should handle single breakdown item with delivery rejected status`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = "Delivery Rejected"),
                ),
                totalOrderedQuantity = BigDecimal("10.0"),
                totalReceivedQuantity = BigDecimal.ZERO,
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.DELIVERY_REJECTED, result.status)
        }

        @Test
        fun `should handle empty breakdown items list`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = emptyList(),
                totalOrderedQuantity = BigDecimal("10.0"),
                totalReceivedQuantity = null,
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            // Note: receiptStatuses.all { it == "Delivery Rejected" } returns true for empty list
            // This is the expected behavior in Kotlin
            assertEquals(PoOverviewStatusEnum.DELIVERY_REJECTED, result.status)
        }

        @Test
        fun `should handle breakdown items with null statuses`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                breakdownItems = listOf(
                    createBreakdownItem(status = null),
                    createBreakdownItem(status = "Received"),
                ),
                totalOrderedQuantity = BigDecimal("20.0"),
                totalReceivedQuantity = BigDecimal("20.0"),
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(PoOverviewStatusEnum.RECEIVED_ACCURATE, result.status)
        }
    }

    @Nested
    @DisplayName("packaging type mapping")
    inner class PackagingTypeMappingTest {

        @Test
        fun `should map packaging type to CASE when packagingType is case`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "case"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
        }

        @Test
        fun `should map packaging type to CASE when packagingType is Case (mixed case)`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "Case"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
        }

        @Test
        fun `should map packaging type to CASE when packagingType is CASE (uppercase)`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "CASE"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
        }

        @Test
        fun `should map packaging type to UNIT when packagingType is unit`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "unit"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        }

        @Test
        fun `should map packaging type to UNIT when packagingType is kg`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "kg"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        }

        @Test
        fun `should map packaging type to UNIT when packagingType is piece`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "piece"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        }

        @Test
        fun `should map packaging type to UNIT when packagingType is empty string`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = ""
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        }

        @Test
        fun `should map packaging type to UNIT when packagingType is null-like string`() {
            // given
            val transferOrderOverviewModel = createTransferOrderOverviewModel(
                packagingType = "null"
            )

            // when
            val result = transferOrderOverviewModel.toApiResponse()

            // then
            assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        }
    }

    private fun createTransferOrderOverviewModel(
        breakdownItems: List<TransferOrderBreakdownItem>,
        totalOrderedQuantity: BigDecimal,
        totalReceivedQuantity: BigDecimal?
    ): TransferOrderOverviewModel =
        TransferOrderOverviewModel(
            transferOrderId = UUID.randomUUID(),
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            sourceDcCode = "DC001",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            packagingType = "case",
            totalOrderedQuantity = totalOrderedQuantity,
            totalReceivedQuantity = totalReceivedQuantity,
            totalCasesReceived = 5,
            totalPriceOrdered = BigDecimal("10000"),
            totalPriceReceived = BigDecimal("9500"),
            casePrice = BigDecimal("2000"),
            weightedAvgCaseSize = BigDecimal("2.0"),
            weightedAvgCaseSizeReceived = BigDecimal("2.0"),
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            transferOrderBreakdown = breakdownItems,
            scheduledDeliveryTime = null,
            brand = "HF",
            currency = "USD",
            supplierName = "Supplier A",
            supplierCode = "SUP-001",
            skuId = UUID.randomUUID(),
            reasonText = "Test reason",
            shippingMethod = "Truck"
        )

    private fun createTransferOrderOverviewModel(
        packagingType: String,
        breakdownItems: List<TransferOrderBreakdownItem> = listOf(
            createBreakdownItem(status = "Received")
        ),
        totalOrderedQuantity: BigDecimal = BigDecimal("10.0"),
        totalReceivedQuantity: BigDecimal? = BigDecimal("10.0")
    ): TransferOrderOverviewModel =
        TransferOrderOverviewModel(
            transferOrderId = UUID.randomUUID(),
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            sourceDcCode = "DC001",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            packagingType = packagingType,
            totalOrderedQuantity = totalOrderedQuantity,
            totalReceivedQuantity = totalReceivedQuantity,
            totalCasesReceived = 5,
            totalPriceOrdered = BigDecimal("10000"),
            totalPriceReceived = BigDecimal("9500"),
            casePrice = BigDecimal("2000"),
            weightedAvgCaseSize = BigDecimal("2.0"),
            weightedAvgCaseSizeReceived = BigDecimal("2.0"),
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            transferOrderBreakdown = breakdownItems,
            scheduledDeliveryTime = null,
            brand = "HF",
            currency = "USD",
            supplierName = "Supplier A",
            supplierCode = "SUP-001",
            skuId = UUID.randomUUID(),
            reasonText = "Test reason",
            shippingMethod = "Truck"
        )

    private fun createBreakdownItem(
        status: String?,
        destinationDcCode: String = "DC002",
        week: String = "2024-W01",
        quantityReceived: BigDecimal? = BigDecimal("10.0"),
        totalQuantity: BigDecimal = BigDecimal("10.0")
    ): TransferOrderBreakdownItem =
        TransferOrderBreakdownItem(
            status = status,
            destinationDcCode = destinationDcCode,
            week = YearWeek(week),
            quantityReceived = quantityReceived,
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            totalQuantity = totalQuantity,
            casePrice = BigDecimal("2500.0"),
            isVoided = false,
            currency = "USD",
        )
}
