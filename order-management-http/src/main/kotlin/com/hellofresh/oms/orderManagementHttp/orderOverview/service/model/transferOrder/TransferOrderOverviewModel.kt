package com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder

import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class TransferOrderOverviewModel(
    val transferOrderId: UUID,
    val transferOrderNumber: String,
    val supplierName: String,
    val supplierCode: String,
    val skuCode: String,
    val brand: String,
    val sourceDcCode: String,
    val skuName: String,
    val category: String,
    val skuId: UUID,
    val skuUom: String?,
    val packagingType: String,
    val totalOrderedQuantity: BigDecimal,
    val totalReceivedQuantity: BigDecimal?,
    val totalCasesReceived: Long?,
    val totalPriceOrdered: BigDecimal,
    val totalPriceReceived: BigDecimal?,
    val currency: String,
    val casePrice: BigDecimal?,
    val weightedAvgCaseSize: BigDecimal?,
    val weightedAvgCaseSizeReceived: BigDecimal?,
    val assignedBuyerFirstName: String?,
    val assignedBuyerLastName: String?,
    val scheduledDeliveryTime: LocalDateTime?,
    val transferOrderBreakdown: List<TransferOrderBreakdownItem>
)
