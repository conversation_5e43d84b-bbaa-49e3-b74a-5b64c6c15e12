package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal

data class TransferOrderBreakdownItem(
    val status: String?,
    val destinationDcCode: String,
    val week: YearWeek,
    val quantityReceived: BigDecimal?,
    val casesReceived: Int?,
    val caseSize: BigDecimal?,
    val totalQuantity: BigDecimal,
    val casePrice: BigDecimal,
    val isVoided: Boolean,
    val currency: String,
)
