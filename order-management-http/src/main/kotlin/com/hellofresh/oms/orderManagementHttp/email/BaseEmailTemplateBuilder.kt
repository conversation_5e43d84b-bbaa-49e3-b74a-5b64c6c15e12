package com.hellofresh.oms.orderManagementHttp.email

class BaseEmailTemplateBuilder : EmailTemplateBuilder {

    // TODO: Add support for multiple recipients
    // TODO: Generate subject
    override fun getEmailTemplate(userEmail: String) = EmailTemplate(
        from = userEmail,
        recipients = setOf(userEmail),
        ccs = emptySet(),
        subject = "Purchase Order Mail Test",
        content = "This is a test email for the Purchase Order system. Please ignore this email as it is only for testing purposes."
    )
}
