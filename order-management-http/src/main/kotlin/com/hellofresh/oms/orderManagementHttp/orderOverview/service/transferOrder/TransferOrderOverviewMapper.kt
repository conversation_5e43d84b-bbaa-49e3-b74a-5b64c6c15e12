package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrderBreakdownItem as TransferOrderBreakdownItemDto
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.PoStatusParams
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.calculateHjStatus
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder.TransferOrderOverviewModel
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.toLocalDateTime
import java.math.BigDecimal
import java.math.RoundingMode

fun TransferOrderOverviewModel.toApiResponse(): TransferOrdersOverviewResponseInner = with(this) {
    TransferOrdersOverviewResponseInner(
        transferOrderId = transferOrderId,
        transferOrderNumber = transferOrderNumber,
        supplierName = supplierName,
        supplierCode = supplierCode,
        status = calculateConsolidatedStatus(
            transferOrderDetails = transferOrderBreakdown,
            totalOrderedQuantity = totalOrderedQuantity,
            totalReceivedQuantity = totalReceivedQuantity,
        ),
        skuCode = skuCode,
        sourceDcCode = sourceDcCode,
        brand = brand,
        skuName = skuName,
        category = category,
        skuUom = skuUom,
        packagingType = if (packagingType.lowercase() == "case") {
            TransferOrdersOverviewResponseInner.PackagingType.CASE
        } else {
            TransferOrdersOverviewResponseInner.PackagingType.UNIT
        },
        totalOrderedQuantity = totalOrderedQuantity,
        totalReceivedQuantity = totalReceivedQuantity,
        totalCasesReceived = totalCasesReceived,
        totalPriceOrdered = totalPriceOrdered.toMoneyDto(currency),
        totalPriceReceived = totalPriceReceived?.toMoneyDto(currency),
        casePrice = casePrice?.toMoneyDto(currency),
        weightedAvgCaseSize = weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
        assignedBuyer = assignedBuyerFirstName?.let { fn ->
            assignedBuyerLastName?.let { ln -> "$fn $ln" }
        },
        scheduledDeliveryTime = scheduledDeliveryTime,
        transferOrderBreakdown = transferOrderBreakdown.map { it.toDto() },
    )
}

fun TransferOrderBreakdownItem.toDto(): TransferOrderBreakdownItemDto =
    TransferOrderBreakdownItemDto(
        status = calculateHjStatus(
            PoStatusParams(
                isVoided = isVoided,
                hjStatus = status,
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = quantityReceived,
                quantityOrdered = totalQuantity,
            ),
        ),
        destinationDcCode = destinationDcCode,
        week = week.value,
        quantityReceived = quantityReceived,
        casesReceived = casesReceived,
        caseSize = caseSize,
        totalQuantity = totalQuantity,
        casePrice = casePrice.toMoneyDto(currency = currency),
        reasonText = reasonText,
        shippingMethod = shippingMethod,
    )

fun List<TransferOrderStatusDetails>.toOverviewModel(brand: String): TransferOrderOverviewModel {
    val first = first()
    val totalCasesReceived = sumOf { it.casesReceived ?: 0L }
    val totalOrderedQuantity = sumOf { it.quantityOrdered }
    val totalReceivedQuantity = mapNotNull { it.quantityReceived }.sumOf { it }

    val weightedAvgCaseSize = calculateWeightedAverage(
        mapNotNull { it.caseSize?.toBigDecimal()?.let { size -> size to it.quantityOrdered.toBigDecimal() } },
    )
    val weightedAvgCaseSizeReceived = calculateWeightedAverage(
        mapNotNull { it.caseSize?.toBigDecimal()?.let { size -> size to (it.quantityReceived ?: BigDecimal.ZERO) } },
    )

    val casePrice = if (totalCasesReceived != 0L) {
        first.totalPrice / totalCasesReceived.toBigDecimal()
    } else {
        0L.toBigDecimal()
    }
    val totalPriceReceived = first.casesReceived?.toBigDecimal()?.times(casePrice)

    return TransferOrderOverviewModel(
        transferOrderId = first.transferOrderId,
        transferOrderNumber = first.transferOrderNumber,
        supplierName = first.supplierName,
        supplierCode = first.supplierCode,
        skuCode = first.skuCode,
        brand = brand,
        sourceDcCode = first.sourceDcCode,
        skuName = first.skuName,
        category = first.category,
        skuId = first.skuId,
        skuUom = first.skuUom,
        packagingType = first.packagingType,
        totalOrderedQuantity = totalOrderedQuantity.toBigDecimal(),
        totalReceivedQuantity = totalReceivedQuantity,
        totalCasesReceived = totalCasesReceived,
        totalPriceOrdered = first.totalPrice,
        totalPriceReceived = totalPriceReceived,
        currency = first.currency,
        casePrice = casePrice,
        weightedAvgCaseSize = weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = first.assignedBuyerFirstName,
        assignedBuyerLastName = first.assignedBuyerLastName,
        scheduledDeliveryTime = first.scheduledDeliveryTime?.toLocalDateTime(),
        transferOrderBreakdown = map { it.toBreakdownItem() },
    )
}

fun TransferOrderStatusDetails.toBreakdownItem(): TransferOrderBreakdownItem =
    TransferOrderBreakdownItem(
        status = receiptStatus,
        destinationDcCode = destinationDcCode,
        week = YearWeek(week),
        quantityReceived = quantityReceived,
        casesReceived = casesReceived?.toInt(),
        caseSize = caseSize?.toBigDecimal(),
        totalQuantity = quantityOrdered.toBigDecimal(),
        casePrice = totalPrice / quantityOrdered.toBigDecimal(),
        reasonText = reasonText,
        shippingMethod = shippingMethod,
        isVoided = isVoided,
        currency = currency,
    )

private fun calculateWeightedAverage(values: List<Pair<BigDecimal, BigDecimal>>): BigDecimal? {
    val totalWeight = values.sumOf { it.second }
    if (values.isEmpty() || totalWeight == BigDecimal.ZERO) return null

    val weightedSum = values.sumOf { (value, weight) -> value * weight }
    return weightedSum.divide(totalWeight, 2, RoundingMode.HALF_UP)
}

private fun calculateConsolidatedStatus(
    transferOrderDetails: List<TransferOrderBreakdownItem>,
    totalOrderedQuantity: BigDecimal,
    totalReceivedQuantity: BigDecimal?
): PoOverviewStatusEnum {
    val statuses = transferOrderDetails.map { it.status }
    val allRejected = statuses.all { it == "Delivery Rejected" }
    val hasPartialRejection = statuses.any { it == "Delivery Rejected" || it == "Received - Partial Rejection" }

    return when {
        allRejected -> PoOverviewStatusEnum.DELIVERY_REJECTED
        hasPartialRejection -> PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION
        totalReceivedQuantity != null && totalReceivedQuantity < totalOrderedQuantity -> PoOverviewStatusEnum.RECEIVED_UNDER
        totalReceivedQuantity != null && totalReceivedQuantity > totalOrderedQuantity -> PoOverviewStatusEnum.RECEIVED_OVER
        totalReceivedQuantity != null && totalReceivedQuantity == totalOrderedQuantity -> PoOverviewStatusEnum.RECEIVED_ACCURATE
        else -> PoOverviewStatusEnum.IN_PROGRESS_HJ
    }
}

private fun BigDecimal.toMoneyDto(currency: String) = MoneyDto(
    amount = this.setScale(MoneyDomain.PRECISION.value, RoundingMode.HALF_EVEN).toPlainString(),
    currency = currency,
)
