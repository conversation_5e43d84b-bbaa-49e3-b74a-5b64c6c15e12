TransferOrdersOverviewResponse:
  type: array
  items:
    required:
      - transfer_order_id
      - transfer_order_number
      - supplier_name
      - supplier_code
      - status
      - brand
      - scheduled_delivery_date
      - sku_code
      - sku_id
      - source_dc_code
      - sku_name
      - category
      - packaging_type
      - total_ordered_quantity
      - total_price_ordered
      - transfer_order_breakdown
    type: object
    properties:
      transfer_order_id:
        type: string
        format: uuid
        example: "123e4567-e89b-12d3-a456-************"
        displayName: "Transfer Order ID"
      transfer_order_number:
        type: string
        example: "TO-2024-001"
        displayName: "Transfer Order Number"
      supplier_name:
        type: string
        example: "Fresh Produce Supplier"
        displayName: "Supplier Name"
      supplier_code:
        type: string
        example: "123456"
        displayName: "Supplier Code"
      brand:
        type: string
        example: "HF"
      scheduled_delivery_time:
        type: string
        format: date-time
        example: "2024-04-15T10:00:00Z"
        displayName: "Scheduled Delivery Time"
      status:
        ref: './enum.yaml#/PoOverviewStatusEnum'
      sku_code:
        type: string
        example: "SKU-001"
        displayName: "SKU Code"
      sku_id:
        type: string
        format: uuid
        example: "123e4567-e89b-12d3-a456-************"
        displayName: "SKU ID"
      source_dc_code:
        type: string
        example: "DC001"
        displayName: "Source DC Code"
      sku_name:
        type: string
        example: "Fresh Tomatoes"
        displayName: "SKU Name"
      category:
        type: string
        example: "Fresh"
        displayName: "Category"
      sku_uom:
        type: string
        nullable: true
        example: "kg"
        displayName: "SKU Unit of Measure"
      packaging_type:
        type: string
        enum: [case, unit]
        example: "case"
        displayName: "Packaging Type"
      total_ordered_quantity:
        type: number
        format: decimal
        example: 100.0
        displayName: "Total Ordered Quantity"
      total_received_quantity:
        type: number
        format: decimal
        nullable: true
        example: 95.0
        displayName: "Total Received Quantity"
      total_cases_received:
        type: integer
        format: int64
        nullable: true
        example: 47
        displayName: "Total Cases Received"
      total_price_ordered:
        $ref: './money.yaml#/MoneyDto'
        description: "The total price of the PO item in the currency of the PO"
      total_price_received:
        $ref: './money.yaml#/MoneyDto'
        description: "The total price of the PO item in the currency of the PO"
      case_price:
        $ref: './money.yaml#/MoneyDto'
        description: "The total price of the PO item in the currency of the PO"
      weighted_avg_case_size:
        type: number
        format: decimal
        nullable: true
        example: 2.0
        displayName: "Weighted Average Case Size"
      weighted_avg_case_size_received:
        type: number
        format: decimal
        nullable: true
        example: 2.0
        displayName: "Weighted Average Case Size Received"
      assigned_buyer:
        type: string
        example: "Alice Johnson"
      transfer_order_breakdown:
        type: array
        items:
          $ref: '#/TransferOrderBreakdownItem'

TransferOrderBreakdownItem:
  required:
    - status
    - destination_dc_code
    - week
    - total_quantity
    - case_price
  type: object
  properties:
    status:
      ref: './enum.yaml#/PoOverviewStatusEnum'
    destination_dc_code:
      type: string
      example: "DC002"
      displayName: "Destination DC Code"
    week:
      type: string
      pattern: ^20\d{2}-W\d{2}$
      example: "2024-W15"
      displayName: "Week"
    quantity_received:
      type: number
      format: decimal
      nullable: true
      example: 10.0
      displayName: "Quantity Received"
    cases_received:
      type: integer
      nullable: true
      example: 5
      displayName: "Cases Received"
    case_size:
      type: number
      format: decimal
      nullable: true
      example: 2.0
      displayName: "Case Size"
    total_quantity:
      type: number
      format: decimal
      example: 10.0
      displayName: "Total Quantity"
    case_price:
      $ref: './money.yaml#/MoneyDto'
      description: "The total price of the PO item in the currency of the PO"
    reason_text:
      type: string
      nullable: true
      example: "Stock transfer"
      displayName: "Reason Text"
    shipping_method:
      type: string
      nullable: true
      example: "Truck"
      displayName: "Shipping Method"
