{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Order management service board", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3133657, "links": [], "liveNow": false, "panels": [{"collapsed": true, "datasource": {"type": "", "uid": ""}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 140, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "id": 138, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "11.3.0-76537", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(order_management_tapioca_not_synced)", "interval": "", "legendFormat": "", "range": true, "refId": "A"}], "title": "Purchase Orders Not Synced Within 1 Minute", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 6, "y": 1}, "id": 141, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(order_management_tapioca_sync_time_millis_total{container=\"oms-purchase-order-synchronizer-app\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Delay", "range": true, "refId": "B"}], "title": "Response Time", "type": "timeseries"}], "title": "OMG to OT Sync Process", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 149, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "id": 150, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0-76537", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "expr": "sum(edited_po_with_existing_grn_total{container=\"oms-order-management-http-app\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Edited POs with existing GRNs", "type": "stat"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "id": 151, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "expr": "sum by (market) (edited_po_with_existing_grn_total{container=\"oms-order-management-http-app\"})", "instant": false, "legendFormat": "Market [{{market}}]", "range": true, "refId": "A"}], "title": "Edited POs with existing GRNs by Market", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 152, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "expr": "sum by (po_status) (edited_po_with_existing_grn_total{container=\"oms-order-management-http-app\"})", "instant": false, "legendFormat": "PO Status [{{po_status}}]", "range": true, "refId": "A"}], "title": "Edited POs with existing GRNs by PO Status", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 153, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "expr": "sum by (market, po_status) (edited_po_with_existing_grn_total{container=\"oms-order-management-http-app\"})", "instant": false, "legendFormat": "Market [{{market}}] PO Status [{{po_status}}]", "range": true, "refId": "A"}], "title": "Edited POs with existing GRNs by Market and PO Status", "type": "timeseries"}], "title": "GRN", "type": "row"}, {"collapsed": true, "datasource": {"type": "", "uid": ""}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 109, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 142, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.scm.registry.dc.v1beta1.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Distribution Center Consumer Exception", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 111, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+(rawevents.facility.*.v1|public.planning.facility.v1).+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Supplier Consumer Exception", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 113, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+(rawevents.culinarysku.*|public.planning.culinarysku.v1).+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "SKU Consumer Exception", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 120, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.planning.suppliersku.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "SupplierSKU Consumer Exception", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 128, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.planning.suppliersku.pricing.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Supplier SKU Pricing Consumer Exception", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 114, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.planning.supplier.default-ship-methods.v1.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Ship Method Consumer Exception", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 130, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.supply.procurement.purchase-order.v1.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Purchase Order Consumer Exception", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "id": 145, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.planning.suppliersku.packaging.v1.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Supplier SKU Packaging Consumer Exception", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}, "id": 147, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (result) (increase(spring_kafka_listener_seconds_count{container=\"oms-kafka-processor-app\", name=~\".+public.distribution-center.inbound.goods-received-note.v1.+\"}[$__rate_interval]))", "legendFormat": "{{result}}", "range": true, "refId": "A"}], "title": "Goods Received Note Consumer Exception", "type": "timeseries"}], "title": "Kafka Consumer Exception", "type": "row"}, {"collapsed": true, "datasource": {"type": "", "uid": ""}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 101, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 103, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-supplier.*\", topic=~\"public.planning.facility.v1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Supplier Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 144, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-distributionCenter.*\", topic=~\"public.scm.registry.dc.v1beta1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Distribution Center Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 107, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-ship-method.*\", topic=~\"public.planning.supplier.default-ship-methods.*\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Ship Method Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 105, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-sku.*\", topic=~\"rawevents.culinarysku.*|public.planning.culinarysku.v1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "SKU Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 127, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-supplierSkuPrice.v3\", topic=~\"public.planning.suppliersku.pricing.*\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Supplier SKU Pricing Consumer Lag", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 125, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-supplierSku.v2\", topic=~\"public.planning.suppliersku.*\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "SupplierSKU Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 146, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-supplierSkuPackaging.v1\", topic=~\"public.planning.suppliersku.packaging.v1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Supplier SKU Packaging Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 132, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-purchaseOrder.v2\", topic=~\"public.supply.procurement.purchase-order.v1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Purchase Order Consumer Lag", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 148, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (topic, name) (kafka_consumer_group_rep_lag{name=~\"oms-kafka-processor-goodsReceivedNote.v2\", topic=~\"public.distribution-center.inbound.goods-received-note.v1\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Goods Received Note Consumer Lag", "type": "timeseries"}], "title": "Kafka Lag", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 8, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "id": 6, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container)(increase(logback_events_total{container=~\"oms-.*\", level=\"info\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "INFO logs", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "id": 98, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container)(increase(logback_events_total{container=~\"oms-.*\", level=\"warn\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "WARN logs", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 12}, "id": 97, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container)(increase(logback_events_total{container=~\"oms-.*\", level=\"error\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "ERROR logs", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 12}, "id": 99, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container)(increase(logback_events_total{container=~\"oms-.*\", level=\"debug\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "DEBUG logs", "type": "timeseries"}], "title": "Log Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 18, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "id": 4, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (method, status, uri, content_type, accept_header) (increase(http_server_requests_seconds_count{container=\"oms-order-management-http-app\", uri!~\".*actuator.*\", uri!~\".*swagger.*\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - [Content-type: {{content_type}}] [Accept: {{accept_header}}] {{uri}}", "range": true, "refId": "A"}], "title": "Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "id": 2, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (method, status, uri) (irate(http_server_requests_seconds_sum{container=\"oms-order-management-http-app\", uri!~\".*actuator.*\"}[5m])) / \nsum by (method, status, uri) (irate(http_server_requests_seconds_count{container=\"oms-order-management-http-app\", uri!~\".*actuator.*\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "range": true, "refId": "B"}], "title": "Response Time", "type": "timeseries"}], "title": "HTTP Server Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "", "uid": ""}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 136, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 133, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (method, status, uri) (increase(http_client_requests_seconds_count{container=\"oms-purchase-order-synchronizer-app\", uri!~\".*actuator.*\", uri!~\".*swagger.*\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "range": true, "refId": "A"}], "title": "Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 134, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (method, status, uri) (irate(http_client_requests_seconds_count{container=\"oms-purchase-order-synchronizer-app\", uri!~\".*actuator.*\"}[5m])) / \nsum by (method, status, uri) (irate(http_client_requests_seconds_count{container=\"oms-purchase-order-synchronizer-app\", uri!~\".*actuator.*\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "range": true, "refId": "B"}], "title": "Response Time", "type": "timeseries"}], "title": "HTTP Client Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 34, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 36, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container) (hikaricp_connections_active{container=~\"oms-.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Active [ {{container}} ]", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(hikaricp_connections_idle{container=~\"oms-.*\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Total Idle", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(hikaricp_connections_pending{container=~\"oms-.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total Pending", "range": true, "refId": "C"}], "title": "Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container) (increase(hikaricp_connections_creation_seconds_sum{container=~\"oms-.*\"}[1m])) / ignoring() group_left sum by (container) (increase(hikaricp_connections_creation_seconds_count{container=~\"oms-.*\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "refId": "A"}], "title": "Connection Creation Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 16}, "id": 42, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container) (increase(hikaricp_connections_usage_seconds_sum{container=~\"oms-.*\"}[1m])) / ignoring() group_left sum by (container) (increase(hikaricp_connections_usage_seconds_count{container=~\"oms-.*\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "refId": "A"}], "title": "Connection Usage Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 16}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum by (container) (increase(hikaricp_connections_acquire_seconds_sum{container=~\"oms-.*\"}[1m])) / ignoring() group_left sum by (container) (increase(hikaricp_connections_acquire_seconds_count{container=~\"oms-.*\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{container}}", "range": true, "refId": "A"}], "title": "Connection Acquire Time", "type": "timeseries"}], "title": "HikariCP Statistics", "type": "row"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": ["scm", "procurement", "order-management"], "templating": {"list": [{"current": {"selected": false, "text": "metrics_live", "value": "metrics_live"}, "hide": 0, "includeAll": false, "label": "Metrics Datasource", "multi": false, "name": "metrics_ds", "options": [], "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Order Management Service", "uid": "i5rArodVz", "weekStart": ""}